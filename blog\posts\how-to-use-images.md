---
title: 如何在博客中使用图片
description: 演示如何在 VitePress 博客文章中使用图片资源
date: 2025-08-07
author:
  name: Blog Author
  link: https://github.com/author
---

# 如何在博客中使用图片

这篇文章演示了如何在 VitePress 博客中使用图片资源。

## 使用 public 目录中的图片

VitePress 会自动处理 `blog/public` 目录下的静态资源。你可以直接引用这些文件：

### 示例：引用 logo

<!-- ```markdown
![Logo](./images/pic.svg)
```

实际效果：

![Logo](./images/pic.svg) -->

## 图片路径规则

1. **绝对路径**：以 `/blog/` 开头，指向 `blog/public` 目录
   ```markdown
   ![图片描述](/your-image.png)
   ```

2. **相对路径**：相对于当前 markdown 文件
   ```markdown
   ![图片描述](./images/your-image.png)
   ```

## 添加新图片的步骤

1. 将图片文件放入 `blog/public` 目录
2. 在 markdown 文件中使用 `/blog/filename.ext` 路径引用
3. 运行 `pnpm blog:build` 重新构建

## 图片优化建议

- 使用适当的图片格式（PNG、JPG、SVG、WebP）
- 压缩图片以减少文件大小
- 为图片添加有意义的 alt 文本
- 考虑使用响应式图片

## HTML 标签方式

你也可以使用 HTML `<img>` 标签获得更多控制：

```html
<img src="./images/pic.svg" alt="Logo" width="100" height="100" />
```

这样你就可以在博客文章中自由使用图片了！
