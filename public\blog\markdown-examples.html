<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Markdown Extension Examples | Blog</title>
    <meta name="description" content="A VitePress Site">
    <meta name="generator" content="VitePress v1.6.4">
    <link rel="preload stylesheet" href="/blog/assets/style.Begsi9jm.css" as="style">
    <link rel="preload stylesheet" href="/blog/vp-icons.css" as="style">
    
    <script type="module" src="/blog/assets/app.D_BBvIzL.js"></script>
    <link rel="preload" href="/blog/assets/inter-roman-latin.Di8DUHzh.woff2" as="font" type="font/woff2" crossorigin="">
    <link rel="modulepreload" href="/blog/assets/chunks/theme.C1Bas7vt.js">
    <link rel="modulepreload" href="/blog/assets/chunks/framework.DIBmnIcf.js">
    <link rel="modulepreload" href="/blog/assets/markdown-examples.md.Dl8fhSOa.lean.js">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"auto",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
  </head>
  <body>
    <div id="app"><div class="Layout" data-v-642b817b><!--[--><!--]--><!--[--><span tabindex="-1" data-v-1de352b9></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-1de352b9>Skip to content</a><!--]--><!----><header class="VPNav" data-v-642b817b data-v-e8e79344><div class="VPNavBar" data-v-e8e79344 data-v-a9bc4c2c><div class="wrapper" data-v-a9bc4c2c><div class="container" data-v-a9bc4c2c><div class="title" data-v-a9bc4c2c><div class="VPNavBarTitle has-sidebar" data-v-a9bc4c2c data-v-aba688c7><a class="title" href="/blog/" data-v-aba688c7><!--[--><!--]--><!----><span data-v-aba688c7>Blog</span><!--[--><!--]--></a></div></div><div class="content" data-v-a9bc4c2c><div class="content-body" data-v-a9bc4c2c><!--[--><!--]--><div class="VPNavBarSearch search" data-v-a9bc4c2c><!----></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-a9bc4c2c data-v-19d0c076><span id="main-nav-aria-label" class="visually-hidden" data-v-19d0c076> Main Navigation </span><!--[--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/blog/" tabindex="0" data-v-19d0c076 data-v-dc1cc5dd><!--[--><span data-v-dc1cc5dd>Home</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink active" href="/blog/markdown-examples.html" tabindex="0" data-v-19d0c076 data-v-dc1cc5dd><!--[--><span data-v-dc1cc5dd>Examples</span><!--]--></a><!--]--><!--]--></nav><!----><div class="VPNavBarAppearance appearance" data-v-a9bc4c2c data-v-013018db><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="false" data-v-013018db data-v-198d9ae7 data-v-dd2b1ac1><span class="check" data-v-dd2b1ac1><span class="icon" data-v-dd2b1ac1><!--[--><span class="vpi-sun sun" data-v-198d9ae7></span><span class="vpi-moon moon" data-v-198d9ae7></span><!--]--></span></span></button></div><div class="VPSocialLinks VPNavBarSocialLinks social-links" data-v-a9bc4c2c data-v-c28d7ee2 data-v-026eecd3><!--[--><a class="VPSocialLink no-icon" href="https://github.com/vuejs/vitepress" aria-label="github" target="_blank" rel="noopener" data-v-026eecd3 data-v-13066e67><span class="vpi-social-github"></span></a><!--]--></div><div class="VPFlyout VPNavBarExtra extra" data-v-a9bc4c2c data-v-19608f77 data-v-f320e8c7><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-f320e8c7><span class="vpi-more-horizontal icon" data-v-f320e8c7></span></button><div class="menu" data-v-f320e8c7><div class="VPMenu" data-v-f320e8c7 data-v-33d8f694><!----><!--[--><!--[--><!----><div class="group" data-v-19608f77><div class="item appearance" data-v-19608f77><p class="label" data-v-19608f77>Appearance</p><div class="appearance-action" data-v-19608f77><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="false" data-v-19608f77 data-v-198d9ae7 data-v-dd2b1ac1><span class="check" data-v-dd2b1ac1><span class="icon" data-v-dd2b1ac1><!--[--><span class="vpi-sun sun" data-v-198d9ae7></span><span class="vpi-moon moon" data-v-198d9ae7></span><!--]--></span></span></button></div></div></div><div class="group" data-v-19608f77><div class="item social-links" data-v-19608f77><div class="VPSocialLinks social-links-list" data-v-19608f77 data-v-026eecd3><!--[--><a class="VPSocialLink no-icon" href="https://github.com/vuejs/vitepress" aria-label="github" target="_blank" rel="noopener" data-v-026eecd3 data-v-13066e67><span class="vpi-social-github"></span></a><!--]--></div></div></div><!--]--><!--]--></div></div></div><!--[--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-a9bc4c2c data-v-77657797><span class="container" data-v-77657797><span class="top" data-v-77657797></span><span class="middle" data-v-77657797></span><span class="bottom" data-v-77657797></span></span></button></div></div></div></div><div class="divider" data-v-a9bc4c2c><div class="divider-line" data-v-a9bc4c2c></div></div></div><!----></header><div class="VPLocalNav has-sidebar empty" data-v-642b817b data-v-66045814><div class="container" data-v-66045814><button class="menu" aria-expanded="false" aria-controls="VPSidebarNav" data-v-66045814><span class="vpi-align-left menu-icon" data-v-66045814></span><span class="menu-text" data-v-66045814>Menu</span></button><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-66045814 data-v-1e951d9a><button data-v-1e951d9a>Return to top</button><!----></div></div></div><aside class="VPSidebar" data-v-642b817b data-v-5d4efa1a><div class="curtain" data-v-5d4efa1a></div><nav class="nav" id="VPSidebarNav" aria-labelledby="sidebar-aria-label" tabindex="-1" data-v-5d4efa1a><span class="visually-hidden" id="sidebar-aria-label" data-v-5d4efa1a> Sidebar Navigation </span><!--[--><!--]--><!--[--><div class="no-transition group" data-v-3cb554eb><section class="VPSidebarItem level-0 has-active" data-v-3cb554eb data-v-3fc49afd><div class="item" role="button" tabindex="0" data-v-3fc49afd><div class="indicator" data-v-3fc49afd></div><h2 class="text" data-v-3fc49afd>Examples</h2><!----></div><div class="items" data-v-3fc49afd><!--[--><div class="VPSidebarItem level-1 is-link" data-v-3fc49afd data-v-3fc49afd><div class="item" data-v-3fc49afd><div class="indicator" data-v-3fc49afd></div><a class="VPLink link link" href="/blog/markdown-examples.html" data-v-3fc49afd><!--[--><p class="text" data-v-3fc49afd>Markdown Examples</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-3fc49afd data-v-3fc49afd><div class="item" data-v-3fc49afd><div class="indicator" data-v-3fc49afd></div><a class="VPLink link link" href="/blog/api-examples.html" data-v-3fc49afd><!--[--><p class="text" data-v-3fc49afd>Runtime API Examples</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><!--]--><!--[--><!--]--></nav></aside><div class="VPContent has-sidebar" id="VPContent" data-v-642b817b data-v-cd9cd26e><div class="VPDoc has-sidebar has-aside" data-v-cd9cd26e data-v-2f279d96><!--[--><!--]--><div class="container" data-v-2f279d96><div class="aside" data-v-2f279d96><div class="aside-curtain" data-v-2f279d96></div><div class="aside-container" data-v-2f279d96><div class="aside-content" data-v-2f279d96><div class="VPDocAside" data-v-2f279d96 data-v-6fe2c2dd><!--[--><!--]--><!--[--><!--]--><nav aria-labelledby="doc-outline-aria-label" class="VPDocAsideOutline" data-v-6fe2c2dd data-v-2c116cb8><div class="content" data-v-2c116cb8><div class="outline-marker" data-v-2c116cb8></div><div aria-level="2" class="outline-title" id="doc-outline-aria-label" role="heading" data-v-2c116cb8>On this page</div><ul class="VPDocOutlineItem root" data-v-2c116cb8 data-v-424258f6><!--[--><!--]--></ul></div></nav><!--[--><!--]--><div class="spacer" data-v-6fe2c2dd></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-2f279d96><div class="content-container" data-v-2f279d96><!--[--><!--]--><main class="main" data-v-2f279d96><div style="position:relative;" class="vp-doc _blog_markdown-examples" data-v-2f279d96><div><h1 id="markdown-extension-examples" tabindex="-1">Markdown Extension Examples <a class="header-anchor" href="#markdown-extension-examples" aria-label="Permalink to &quot;Markdown Extension Examples&quot;">​</a></h1><p>This page demonstrates some of the built-in markdown extensions provided by VitePress.</p><h2 id="syntax-highlighting" tabindex="-1">Syntax Highlighting <a class="header-anchor" href="#syntax-highlighting" aria-label="Permalink to &quot;Syntax Highlighting&quot;">​</a></h2><p>VitePress provides Syntax Highlighting powered by <a href="https://github.com/shikijs/shiki" target="_blank" rel="noreferrer">Shiki</a>, with additional features like line-highlighting:</p><p><strong>Input</strong></p><div class="language-md vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">md</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">```js{4}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">export default {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  data () {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    return {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      msg: &#39;Highlighted!&#39;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">```</span></span></code></pre></div><p><strong>Output</strong></p><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">export</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> default</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  data</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> () {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">    return</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line highlighted"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      msg: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;Highlighted!&#39;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h2 id="custom-containers" tabindex="-1">Custom Containers <a class="header-anchor" href="#custom-containers" aria-label="Permalink to &quot;Custom Containers&quot;">​</a></h2><p><strong>Input</strong></p><div class="language-md vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">md</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">::: info</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">This is an info box.</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">:::</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">::: tip</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">This is a tip.</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">:::</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">::: warning</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">This is a warning.</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">:::</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">::: danger</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">This is a dangerous warning.</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">:::</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">::: details</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">This is a details block.</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">:::</span></span></code></pre></div><p><strong>Output</strong></p><div class="info custom-block"><p class="custom-block-title">INFO</p><p>This is an info box.</p></div><div class="tip custom-block"><p class="custom-block-title">TIP</p><p>This is a tip.</p></div><div class="warning custom-block"><p class="custom-block-title">WARNING</p><p>This is a warning.</p></div><div class="danger custom-block"><p class="custom-block-title">DANGER</p><p>This is a dangerous warning.</p></div><details class="details custom-block"><summary>Details</summary><p>This is a details block.</p></details><h2 id="more" tabindex="-1">More <a class="header-anchor" href="#more" aria-label="Permalink to &quot;More&quot;">​</a></h2><p>Check out the documentation for the <a href="https://vitepress.dev/guide/markdown" target="_blank" rel="noreferrer">full list of markdown extensions</a>.</p></div></div></main><footer class="VPDocFooter" data-v-2f279d96 data-v-1fb9e5ba><!--[--><!--]--><!----><nav class="prev-next" aria-labelledby="doc-footer-aria-label" data-v-1fb9e5ba><span class="visually-hidden" id="doc-footer-aria-label" data-v-1fb9e5ba>Pager</span><div class="pager" data-v-1fb9e5ba><!----></div><div class="pager" data-v-1fb9e5ba><a class="VPLink link pager-link next" href="/blog/api-examples.html" data-v-1fb9e5ba><!--[--><span class="desc" data-v-1fb9e5ba>Next page</span><span class="title" data-v-1fb9e5ba>Runtime API Examples</span><!--]--></a></div></nav></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><!----><!--[--><!--]--></div></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"api-examples.md\":\"BvL5Rtle\",\"index.md\":\"DY7qmis0\",\"markdown-examples.md\":\"Dl8fhSOa\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"Blog\",\"description\":\"A VitePress Site\",\"base\":\"/blog/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":true,\"themeConfig\":{\"nav\":[{\"text\":\"Home\",\"link\":\"/\"},{\"text\":\"Examples\",\"link\":\"/markdown-examples\"}],\"sidebar\":[{\"text\":\"Examples\",\"items\":[{\"text\":\"Markdown Examples\",\"link\":\"/markdown-examples\"},{\"text\":\"Runtime API Examples\",\"link\":\"/api-examples\"}]}],\"socialLinks\":[{\"icon\":\"github\",\"link\":\"https://github.com/vuejs/vitepress\"}]},\"locales\":{},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>