import { defineConfig } from 'vitepress'
import { genFeed } from './genFeed.js'
// @ts-ignore
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  base: '/bolg/',
  outDir: '../public/bolg', // 路径是相对于 bolg 文件夹的
  vite: {
    plugins: [tailwindcss() as any],
  },
  title: 'The Vue Point',
  description: 'The official blog for the Vue.js project',
  cleanUrls: true,
  head: [
    ['meta', { name: 'twitter:site', content: '@vuejs' }],
    ['meta', { name: 'twitter:card', content: 'summary' }],
    [
      'meta',
      {
        name: 'twitter:image',
        content: 'https://vuejs.org/images/logo.png'
      }
    ],
    [
      'link',
      {
        rel: 'icon',
        type: 'image/x-icon',
        href: '/favicon.ico'
      }
    ]
  ],
  buildEnd: genFeed
})
